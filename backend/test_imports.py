#!/usr/bin/env python3
"""
简单的导入测试脚本
"""

def test_imports():
    """测试所有重要的导入"""
    try:
        print("测试 common.constants 导入...")
        from common.constants import (
            APP_HOME_DIR,
            DEFAULT_MAX_STEP_NUM,
            DEFAULT_AUTO_ACCEPTED_PLAN,
            DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
            AGENT_LLM_MAP,
            SELECTED_SEARCH_ENGINE,
            BUILT_IN_QUESTIONS,
            headers,
            mcp_settings
        )
        print("✅ common.constants 导入成功")
        
        print("测试 logging_config 导入...")
        from logging_config import configure_logging
        print("✅ logging_config 导入成功")
        
        print("测试 web.api.routes.chat 导入...")
        from web.api.routes.chat import router
        print("✅ web.api.routes.chat 导入成功")
        
        print("测试值:")
        print(f"  APP_HOME_DIR: {APP_HOME_DIR}")
        print(f"  DEFAULT_MAX_STEP_NUM: {DEFAULT_MAX_STEP_NUM}")
        print(f"  AGENT_LLM_MAP keys: {list(AGENT_LLM_MAP.keys())}")
        print(f"  SELECTED_SEARCH_ENGINE: {SELECTED_SEARCH_ENGINE}")
        print(f"  BUILT_IN_QUESTIONS count: {len(BUILT_IN_QUESTIONS)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 所有导入测试通过！")
    else:
        print("\n❌ 导入测试失败")
        exit(1)
