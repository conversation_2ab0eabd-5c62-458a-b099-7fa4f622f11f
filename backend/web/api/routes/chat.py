"""聊天相关的API路由处理模块

本模块包含与聊天功能相关的API路由处理函数和事件流处理相关的功能。
"""

import json
from typing import List, Dict, Any, cast
from uuid import uuid4

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from langchain_core.messages import AIMessageChunk, ToolMessage, BaseMessage
from langgraph.types import Command

from common.constants_deprecated import DEFAULT_MAX_STEP_NUM, DEFAULT_AUTO_ACCEPTED_PLAN, DEFAULT_ENABLE_BACKGROUND_INVESTIGATION
from web.models.chat_request import ChatMessage, ChatRequest

router = APIRouter(prefix="/api/chat", tags=["chat"])


def make_event(event_type: str, data: dict[str, any]):
    """创建SSE事件

    Args:
        event_type: 事件类型
        data: 事件数据

    Returns:
        格式化的SSE事件字符串
    """
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"


async def astream_workflow_generator(
    messages: List[Dict[str, Any]],
    thread_id: str,
    max_plan_iterations: int,
    max_step_num: int,
    max_search_results: int,
    auto_accepted_plan: bool,
    interrupt_feedback: str,
    mcp_settings: dict,
    enable_background_investigation: bool,
    graph,
    langfuse_handler,
):
    """生成聊天流式工作流

    Args:
        messages: 聊天消息列表
        thread_id: 会话ID
        max_plan_iterations: 最大计划迭代次数
        max_step_num: 最大步骤数
        max_search_results: 最大搜索结果数
        auto_accepted_plan: 是否自动接受计划
        interrupt_feedback: 中断反馈
        mcp_settings: MCP设置
        enable_background_investigation: 是否启用后台调查
        graph: 图对象
        langfuse_handler: Langfuse回调处理器

    Yields:
        事件流
    """
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": True,
        "enable_background_investigation": enable_background_investigation,
    }
    if not auto_accepted_plan and interrupt_feedback:
        resume_msg = f"[{interrupt_feedback}]"
        # add the last message to the resume message
        if messages:
            resume_msg += f" {messages[-1]['content']}"
        input_ = Command(resume=resume_msg)
    async for agent, _, event_data in graph.astream(
        input_,
        config={
            "thread_id": thread_id,
            "max_plan_iterations": max_plan_iterations,
            "max_step_num": max_step_num,
            "max_search_results": max_search_results,
            "mcp_settings": mcp_settings,
            "callbacks": [langfuse_handler]
        },
        stream_mode=["messages", "updates"],
        subgraphs=True,
    ):
        if isinstance(event_data, dict):
            if "__interrupt__" in event_data:
                yield make_event(
                    "interrupt",
                    {
                        "thread_id": thread_id,
                        "id": event_data["__interrupt__"][0].ns[0],
                        "role": "assistant",
                        "content": event_data["__interrupt__"][0].value,
                        "finish_reason": "interrupt",
                        "options": [
                            {"text": "Edit plan", "value": "edit_plan"},
                            {"text": "Start research", "value": "accepted"},
                        ],
                    },
                )
            continue
        message_chunk, message_metadata = cast(
            tuple[BaseMessage, dict[str, any]], event_data
        )
        event_stream_message: dict[str, any] = {
            "thread_id": thread_id,
            "agent": agent[0].split(":")[0],
            "id": message_chunk.id,
            "role": "assistant",
            "content": message_chunk.content,
        }
        if message_chunk.response_metadata.get("finish_reason"):
            event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                "finish_reason"
            )
        if isinstance(message_chunk, ToolMessage):
            # Tool Message - Return the result of the tool call
            event_stream_message["tool_call_id"] = message_chunk.tool_call_id
            yield make_event("tool_call_result", event_stream_message)
        elif isinstance(message_chunk, AIMessageChunk):
            # AI Message - Raw message tokens
            if message_chunk.tool_calls:
                # AI Message - Tool Call
                event_stream_message["tool_calls"] = message_chunk.tool_calls
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield make_event("tool_calls", event_stream_message)
            elif message_chunk.tool_call_chunks:
                # AI Message - Tool Call Chunks
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield make_event("tool_call_chunks", event_stream_message)
            else:
                # AI Message - Raw message tokens
                yield make_event("message_chunk", event_stream_message)


@router.post("/stream")
async def chat_stream(request: ChatRequest):
    """处理聊天流请求

    Args:
        request: 聊天请求对象

    Returns:
        流式响应对象
    """
    from web.api.app import graph, langfuse_handler

    thread_id = request.thread_id
    if thread_id == "__default__":
        thread_id = str(uuid4())
    return StreamingResponse(
        astream_workflow_generator(
            request.model_dump()["messages"],
            thread_id,
            request.max_plan_iterations,
            DEFAULT_MAX_STEP_NUM,  # 使用常量替代硬编码值
            request.max_search_results,
            DEFAULT_AUTO_ACCEPTED_PLAN,  # 使用常量替代硬编码值
            request.interrupt_feedback,
            mcp_servers.get_mcp_settings(),
            DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,  # 使用常量替代硬编码值
            graph,
            langfuse_handler
        ),
        media_type="text/event-stream",
    )
