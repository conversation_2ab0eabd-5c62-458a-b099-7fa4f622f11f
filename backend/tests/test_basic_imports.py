"""
Basic import tests to ensure modules can be imported correctly.
"""

import pytest


def test_constants_import():
    """Test that constants module can be imported."""
    try:
        from common.constants_deprecated import (
            APP_HOME_DIR,
            DEFAULT_MAX_STEP_NUM,
            DEFAULT_AUTO_ACCEPTED_PLAN,
            DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
            mcp_settings,
            headers
        )
        assert True  # If we get here, imports worked
    except ImportError as e:
        pytest.fail(f"Failed to import constants: {e}")


def test_config_import():
    """Test that config modules can be imported."""
    try:
        from config import BUILT_IN_QUESTIONS, SELECTED_SEARCH_ENGINE, load_yaml_config
        from config import DotDict, Configuration, AGENT_LLM_MAP, SearchEngine, BUILT_IN_QUESTIONS_ZH_CN
        assert True  # If we get here, imports worked
    except ImportError as e:
        pytest.fail(f"Failed to import config modules: {e}")


def test_constants_values():
    """Test basic constants values."""
    from common.constants_deprecated import (
        APP_HOME_DIR,
        DEFAULT_MAX_STEP_NUM,
        DEFAULT_AUTO_ACCEPTED_PLAN,
        DEFAULT_ENABLE_BACKGROUND_INVESTIGATION
    )
    
    assert isinstance(APP_HOME_DIR, str)
    assert isinstance(DEFAULT_MAX_STEP_NUM, int)
    assert isinstance(DEFAULT_AUTO_ACCEPTED_PLAN, bool)
    assert isinstance(DEFAULT_ENABLE_BACKGROUND_INVESTIGATION, bool)


def test_mcp_settings_structure():
    """Test MCP settings structure."""
    from common.constants_deprecated import mcp_settings
    
    assert isinstance(mcp_settings, dict)
    assert "servers" in mcp_settings
    assert isinstance(mcp_settings["servers"], dict)
    
    # Check that we have some expected servers
    servers = mcp_settings["servers"]
    assert len(servers) > 0
    
    # Check structure of first server
    first_server = next(iter(servers.values()))
    assert "name" in first_server
    assert "transport" in first_server
    assert "enabled_tools" in first_server
    assert "add_to_agents" in first_server


def test_config_functions():
    """Test config functions basic functionality."""
    from config import get_config, get_secret, reload_config

    # Test functions exist and are callable
    assert callable(get_config)
    assert callable(get_secret)
    assert callable(reload_config)


def test_agents_config():
    """Test agents configuration."""
    from config import AGENT_LLM_MAP

    assert isinstance(AGENT_LLM_MAP, dict)
    assert len(AGENT_LLM_MAP) > 0

    # Check that all values are valid LLM types
    valid_types = ["basic", "reasoning", "vision"]
    for agent, llm_type in AGENT_LLM_MAP.items():
        assert isinstance(agent, str)
        assert llm_type in valid_types


def test_tools_config():
    """Test tools configuration."""
    from config import SELECTED_SEARCH_ENGINE, SearchEngine

    assert isinstance(SELECTED_SEARCH_ENGINE, str)
    assert hasattr(SearchEngine, 'TAVILY')
    assert hasattr(SearchEngine, 'DUCKDUCKGO')


def test_questions_config():
    """Test questions configuration."""
    from config import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN

    assert isinstance(BUILT_IN_QUESTIONS, list)
    assert isinstance(BUILT_IN_QUESTIONS_ZH_CN, list)
    assert len(BUILT_IN_QUESTIONS) > 0
    assert len(BUILT_IN_QUESTIONS_ZH_CN) > 0
    assert len(BUILT_IN_QUESTIONS) == len(BUILT_IN_QUESTIONS_ZH_CN)


def test_config_from_main_module():
    """Test importing from main config module."""
    from config import BUILT_IN_QUESTIONS, SELECTED_SEARCH_ENGINE, load_yaml_config

    assert isinstance(BUILT_IN_QUESTIONS, list)
    assert isinstance(SELECTED_SEARCH_ENGINE, str)
    assert callable(load_yaml_config)
