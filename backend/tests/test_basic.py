#!/usr/bin/env python3
"""
Basic test to check imports.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

print("Testing basic imports...")

try:
    print("1. Testing DotDict import...")
    from config.core.base_config import DotDict
    print("   DotDict imported successfully")
    
    print("2. Testing DotDict creation...")
    test_dict = {"a": {"b": "c"}}
    dot_dict = DotDict(test_dict)
    print(f"   DotDict created: {dot_dict}")
    print(f"   Access test: {dot_dict.a.b}")
    
    print("3. Testing MCPServerConfig import...")
    from config.settings.mcp_servers import MCPServerConfig
    print("   MCPServerConfig imported successfully")
    
    print("4. Testing MCPServerConfig creation...")
    config = MCPServerConfig(
        name="test",
        transport="http",
        enabled_tools=["tool1"],
        add_to_agents=["agent1"]
    )
    print(f"   MCPServerConfig created: {config}")
    
    print("All basic tests passed!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
