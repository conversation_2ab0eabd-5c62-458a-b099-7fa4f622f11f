#!/usr/bin/env python3
"""
Test script for refactored MCP servers configuration.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

# Mock configuration data based on updated config_daily.yaml (flattened structure)
mock_config_data = {
    'mcp_servers': {
        'vm_coredump': {
            'protocol': 'streamable_http',
            'base_url': 'https://ecs-mcp.alibaba-inc.com',
            'path': '/vm_coredump/mcp/',
            'token': '************************************************',
            'auth': 'token',
            'enabled_tools': [
                'getVmBasicInfo', 'getNcBasicInfo', 'runDiagnose',
                'listReportedOperationalEvents', 'listMonitorExceptions',
                'listActionTrail', 'ScreenShotDiagnose',
                'listOperationRecords', 'listChangeRecords', 'SubmitOps'
            ]
        },
        'diagnose': {
            'protocol': 'streamable_http',
            'base_url': 'http://pre-xmca-cloudbot.aliyun-inc.com',
            'path': '/mcp/mcp/',
            'token': 'M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=',
            'auth': 'bearer',
            'enabled_tools': ['get_vm_coredump']
        },
        'antv': {
            'protocol': 'stdio',
            'args': ['-y', '@antv/mcp-server-chart'],
            'enabled_tools': ['generate_pie_chart']
        }
    }
}

def test_refactored_mcp_settings():
    """Test refactored MCP settings generation."""
    print("Testing refactored get_mcp_settings...")
    
    try:
        # Import here to avoid circular imports
        from config.core.base_config import DotDict
        from config.settings.mcp_servers import get_mcp_settings
        
        # Create mock config
        config = DotDict(mock_config_data)
        
        # Test the function
        result = get_mcp_settings(config)
        print("SUCCESS: Function executed")
        print("Result keys:", list(result.keys()))
        
        if 'servers' in result:
            print("Server names:", list(result['servers'].keys()))
            for name, server_config in result['servers'].items():
                print(f"  {name}:")
                print(f"    transport: {server_config.get('transport')}")
                print(f"    url: {server_config.get('url', 'N/A')}")
                print(f"    enabled_tools: {len(server_config.get('enabled_tools', []))} tools")
                print(f"    headers: {'Yes' if server_config.get('headers') else 'No'}")
                
        return result
        
    except Exception as e:
        print("ERROR:", str(e))
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_refactored_mcp_settings()
    if result:
        print("\nTest completed successfully!")
        # Print expected vs actual comparison
        expected_servers = [ 'vm_coredump','cloudbot', 'antv']
        actual_servers = list(result['servers'].keys()) if 'servers' in result else []
        print(f"Expected servers: {expected_servers}")
        print(f"Actual servers: {actual_servers}")
        print(f"Match: {set(expected_servers) == set(actual_servers)}")
    else:
        print("\nTest failed!")
        sys.exit(1)
