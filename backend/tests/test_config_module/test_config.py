import os
import pytest
import tempfile
import yaml

# Import current modules to test
from common.constants_deprecated import (
    APP_HOME_DIR,
    DEFAULT_MAX_STEP_NUM,
    DEFAULT_AUTO_ACCEPTED_PLAN,
    DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
    mcp_settings,
    headers
)
from config import get_secret, DotDict, load_yaml_config
from config.settings.agents import AGENT_LLM_MAP
from config.settings.tools import SELECTED_SEARCH_ENGINE, SearchEngine
from config.settings.questions import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN
from config.loaders.yaml_loader import YamlConfigLoader


@pytest.fixture(autouse=True)
def cleanup_env_vars():
    """Fixture to clean up environment variables after each test."""
    original_env = os.environ.copy()
    yield
    # Restore original environment variables
    os.environ.clear()
    os.environ.update(original_env)


class TestConstants:
    """Test cases for common.constants module."""

    def test_app_constants_exist(self):
        """Test that basic app constants are defined."""
        assert APP_HOME_DIR is not None
        assert isinstance(APP_HOME_DIR, str)

    def test_workflow_constants_exist(self):
        """Test that workflow constants are defined with correct types."""
        assert isinstance(DEFAULT_MAX_STEP_NUM, int)
        assert isinstance(DEFAULT_AUTO_ACCEPTED_PLAN, bool)
        assert isinstance(DEFAULT_ENABLE_BACKGROUND_INVESTIGATION, bool)

    def test_mcp_settings_structure(self):
        """Test that MCP settings have correct structure."""
        assert isinstance(mcp_settings, dict)
        assert "servers" in mcp_settings
        assert isinstance(mcp_settings["servers"], dict)

        # Test each server configuration
        for server_name, server_config in mcp_settings["servers"].items():
            assert isinstance(server_name, str)
            assert isinstance(server_config, dict)
            assert "name" in server_config
            assert "transport" in server_config
            assert "enabled_tools" in server_config
            assert "add_to_agents" in server_config

    def test_headers_structure(self):
        """Test that headers have correct structure."""
        assert isinstance(headers, dict)
        assert "Content-Type" in headers
        assert "Authorization" in headers


class TestConfigLoader:
    """Test cases for config.config module."""

    def test_dot_dict_functionality(self):
        """Test DotDict class functionality."""
        test_dict = {"key1": "value1", "nested": {"key2": "value2"}}
        dot_dict = DotDict(test_dict)

        assert dot_dict.key1 == "value1"
        assert dot_dict.nested.key2 == "value2"

    def test_get_secret_function_exists(self):
        """Test that get_secret function exists and is callable."""
        # Test that the function exists and is callable
        assert callable(get_secret)

        # Test that the function has the expected signature
        import inspect
        sig = inspect.signature(get_secret)
        params = list(sig.parameters.keys())
        assert 'secret' in params
        assert 'pub_name' in params

        # Test default parameter value
        assert sig.parameters['pub_name'].default == "ecs-deep-diagnose_aone_key"

    def test_yaml_config_loader_initialization(self):
        """Test YamlConfigLoader initialization."""
        loader = YamlConfigLoader()
        assert loader.config_dir is not None
        assert loader._config_path is not None


class TestYamlLoader:
    """Test cases for config.loader module."""

    def test_replace_env_vars(self):
        """Test environment variable replacement."""
        os.environ["TEST_VAR"] = "test_value"

        loader = YamlConfigLoader()
        result = loader._replace_env_var("$TEST_VAR")
        assert result == "test_value"

        result = loader._replace_env_var("normal_string")
        assert result == "normal_string"

        result = loader._replace_env_var("$NONEXISTENT_VAR")
        assert result == "$NONEXISTENT_VAR"

    def test_process_dict(self):
        """Test dictionary processing for environment variables."""
        os.environ["TEST_VAR"] = "test_value"

        test_dict = {
            "key1": "$TEST_VAR",
            "key2": "normal_value",
            "nested": {
                "key3": "$TEST_VAR"
            }
        }

        loader = YamlConfigLoader()
        result = loader._process_env_vars(test_dict)
        assert result["key1"] == "test_value"
        assert result["key2"] == "normal_value"
        assert result["nested"]["key3"] == "test_value"

    def test_load_yaml_config_nonexistent_file(self):
        """Test loading non-existent YAML file."""
        result = load_yaml_config("nonexistent_file.yaml")
        assert result == {}

    def test_load_yaml_config_valid_file(self):
        """Test loading valid YAML file."""
        test_config = {
            "key1": "value1",
            "key2": {"nested": "value2"}
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_config, f)
            temp_file = f.name

        try:
            result = load_yaml_config(temp_file)
            assert result["key1"] == "value1"
            assert result["key2"]["nested"] == "value2"
        finally:
            os.unlink(temp_file)


class TestAgentsConfig:
    """Test cases for config.agents module."""

    def test_agent_llm_map_structure(self):
        """Test AGENT_LLM_MAP structure."""
        assert isinstance(AGENT_LLM_MAP, dict)

        valid_llm_types = ["basic", "reasoning", "vision"]
        for agent, llm_type in AGENT_LLM_MAP.items():
            assert isinstance(agent, str)
            assert llm_type in valid_llm_types


class TestToolsConfig:
    """Test cases for config.tools module."""

    def test_search_engine_enum(self):
        """Test SearchEngine enum."""
        assert SearchEngine.TAVILY.value == "tavily"
        assert SearchEngine.DUCKDUCKGO.value == "duckduckgo"
        assert SearchEngine.BRAVE_SEARCH.value == "brave_search"
        assert SearchEngine.ARXIV.value == "arxiv"

    def test_selected_search_engine(self):
        """Test SELECTED_SEARCH_ENGINE configuration."""
        assert SELECTED_SEARCH_ENGINE is not None
        assert isinstance(SELECTED_SEARCH_ENGINE, str)


class TestQuestionsConfig:
    """Test cases for config.questions module."""

    def test_built_in_questions_structure(self):
        """Test built-in questions structure."""
        assert isinstance(BUILT_IN_QUESTIONS, list)
        assert len(BUILT_IN_QUESTIONS) > 0
        assert all(isinstance(q, str) for q in BUILT_IN_QUESTIONS)

        assert isinstance(BUILT_IN_QUESTIONS_ZH_CN, list)
        assert len(BUILT_IN_QUESTIONS_ZH_CN) > 0
        assert all(isinstance(q, str) for q in BUILT_IN_QUESTIONS_ZH_CN)

    def test_questions_count_match(self):
        """Test that English and Chinese questions have same count."""
        assert len(BUILT_IN_QUESTIONS) == len(BUILT_IN_QUESTIONS_ZH_CN)
