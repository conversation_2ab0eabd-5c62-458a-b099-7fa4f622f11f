"""
Unit tests for MCP servers configuration.
"""

import pytest
from unittest.mock import patch, MagicMock


class TestMCPServersConfig:
    """Test cases for MCP servers configuration."""

    def test_mcp_server_config_dataclass(self):
        """Test MCPServerConfig dataclass creation."""
        from config.settings.mcp_servers import MCPServerConfig

        config = MCPServerConfig(
            name="test_server",
            transport="streamable_http",
            enabled_tools=["tool1", "tool2"],
            add_to_agents=["agent1"]
        )

        assert config.name == "test_server"
        assert config.transport == "streamable_http"
        assert config.enabled_tools == ["tool1", "tool2"]
        assert config.add_to_agents == ["agent1"]
        assert config.env is None
        assert config.url is None
        assert config.command is None
        assert config.args is None
        assert config.headers is None

    @patch('config.settings.mcp_servers.get_config')
    def test_get_default_headers(self, mock_get_config):
        """Test get_default_headers function."""
        from config.settings.mcp_servers import get_default_headers

        # Mock config structure (flattened)
        mock_config = MagicMock()
        mock_config.mcp_servers.diagnose.token = "test_token_123"
        mock_get_config.return_value = mock_config

        headers = get_default_headers()

        assert headers["Content-Type"] == "application/json"
        assert headers["Authorization"] == "Bearer test_token_123"

    @patch('config.settings.mcp_servers.get_config')
    def test_get_mcp_settings_structure(self, mock_get_config):
        """Test that get_mcp_settings returns correct structure."""
        from config.settings.mcp_servers import get_mcp_settings

        # Mock config structure based on flattened config_daily.yaml
        mock_config = MagicMock()

        # Mock flattened mcp_servers structure
        mock_config.mcp_servers = {
            'diagnose': MagicMock(
                protocol="streamable_http",
                base_url="http://pre-xmca-cloudbot.aliyun-inc.com",
                path="/mcp/mcp/",
                token="M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=",
                auth="bearer",
                enabled_tools=["get_vm_coredump"]
            ),
            'vm_coredump': MagicMock(
                protocol="streamable_http",
                base_url="https://ecs-mcp.alibaba-inc.com",
                path="/vm_coredump/mcp/",
                token="************************************************",
                auth="token",
                enabled_tools=[
                    "getVmBasicInfo", "getNcBasicInfo", "runDiagnose", "listReportedOperationalEvents",
                    "listMonitorExceptions", "listActionTrail", "ScreenShotDiagnose",
                    "listOperationRecords", "listChangeRecords", "SubmitOps"
                ]
            ),
            'antv': MagicMock(
                protocol="stdio",
                args=["-y", "@antv/mcp-server-chart"],
                enabled_tools=["generate_pie_chart"]
            )
        }

        mock_get_config.return_value = mock_config

        settings = get_mcp_settings()

        # Test overall structure
        assert isinstance(settings, dict)
        assert "servers" in settings
        assert isinstance(settings["servers"], dict)

        # Test cloudbot server (mapped from diagnose)
        assert "cloudbot" in settings["servers"]
        cloudbot = settings["servers"]["cloudbot"]
        assert cloudbot["name"] == "cloudbot"
        assert cloudbot["transport"] == "streamable_http"
        assert cloudbot["url"] == "http://pre-xmca-cloudbot.aliyun-inc.com/mcp/mcp/"
        assert cloudbot["enabled_tools"] == ["get_vm_coredump"]
        assert cloudbot["add_to_agents"] == ["researcher"]
        assert "headers" in cloudbot
        assert cloudbot["headers"]["Authorization"] == "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="

        # Test vm_coredump server
        assert "vm_coredump" in settings["servers"]
        vm_coredump = settings["servers"]["vm_coredump"]
        assert vm_coredump["name"] == "vm_coredump"
        assert vm_coredump["transport"] == "streamable_http"
        assert vm_coredump["url"] == "https://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/?token=************************************************"
        assert vm_coredump["add_to_agents"] == ["researcher"]

        # Test antv server
        assert "antv" in settings["servers"]
        antv = settings["servers"]["antv"]
        assert antv["name"] == "antv"
        assert antv["transport"] == "stdio"
        assert antv["command"] == "npx"
        assert antv["args"] == ["-y", "@antv/mcp-server-chart"]
        assert antv["enabled_tools"] == ["generate_pie_chart"]
        assert antv["add_to_agents"] == ["researcher"]

    @patch('config.settings.mcp_servers.get_config')
    def test_get_mcp_settings_missing_config(self, mock_get_config):
        """Test get_mcp_settings with missing configuration."""
        # Mock config with missing mcp_servers
        mock_config = MagicMock()
        del mock_config.mcp_servers  # Remove mcp_servers attribute
        mock_get_config.return_value = mock_config
        
        settings = get_mcp_settings()
        
        # Should return empty servers dict when config is missing
        assert isinstance(settings, dict)
        assert "servers" in settings
        assert settings["servers"] == {}

    @patch('config.settings.mcp_servers.get_config')
    def test_get_mcp_settings_partial_config(self, mock_get_config):
        """Test get_mcp_settings with partial configuration."""
        from config.settings.mcp_servers import get_mcp_settings

        # Mock config with only diagnose server (flattened structure)
        mock_config = MagicMock()
        mock_config.mcp_servers = {
            'diagnose': MagicMock(
                protocol="streamable_http",
                base_url="http://test.com",
                path="/test/",
                token="test_token",
                auth="bearer",
                enabled_tools=["test_tool"]
            )
        }

        mock_get_config.return_value = mock_config

        settings = get_mcp_settings()

        # Should only contain cloudbot server (mapped from diagnose)
        assert len(settings["servers"]) == 1
        assert "cloudbot" in settings["servers"]
        assert "vm_coredump" not in settings["servers"]
        assert "antv" not in settings["servers"]

    def test_mcp_server_config_dict_conversion(self):
        """Test MCPServerConfig to dict conversion."""
        config = MCPServerConfig(
            name="test",
            transport="stdio",
            enabled_tools=["tool1"],
            add_to_agents=["agent1"],
            command="test_command",
            args=["arg1", "arg2"]
        )
        
        config_dict = config.__dict__
        
        assert config_dict["name"] == "test"
        assert config_dict["transport"] == "stdio"
        assert config_dict["enabled_tools"] == ["tool1"]
        assert config_dict["add_to_agents"] == ["agent1"]
        assert config_dict["command"] == "test_command"
        assert config_dict["args"] == ["arg1", "arg2"]
        assert config_dict["env"] is None
        assert config_dict["url"] is None
        assert config_dict["headers"] is None
