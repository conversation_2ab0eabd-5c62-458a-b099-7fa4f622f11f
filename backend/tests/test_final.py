#!/usr/bin/env python3
"""
Final test for refactored MCP configuration.
"""

def test_basic_functionality():
    """Test basic functionality without imports."""
    print("Testing basic functionality...")
    
    # Test data structure matching the new YAML format
    test_config = {
        'mcp_servers': {
            'vm_coredump': {
                'protocol': 'streamable_http',
                'base_url': 'https://ecs-mcp.alibaba-inc.com',
                'path': '/vm_coredump/mcp/',
                'token': 'test_token',
                'auth': 'token',
                'enabled_tools': ['tool1', 'tool2']
            },
            'diagnose': {
                'protocol': 'streamable_http',
                'base_url': 'http://pre-xmca-cloudbot.aliyun-inc.com',
                'path': '/mcp/mcp/',
                'token': 'test_token2',
                'auth': 'bearer',
                'enabled_tools': ['tool3']
            },
            'antv': {
                'protocol': 'stdio',
                'args': ['-y', '@antv/mcp-server-chart'],
                'enabled_tools': ['tool4']
            }
        }
    }
    
    # Simulate the logic from get_mcp_settings
    servers = {}
    
    for server_name, server_config in test_config['mcp_servers'].items():
        # Determine output name
        if server_name == "diagnose":
            output_name = "cloudbot"
        else:
            output_name = server_name
            
        # Build basic config
        protocol = server_config.get('protocol', 'streamable_http')
        
        config_data = {
            'name': output_name,
            'transport': protocol,
            'enabled_tools': server_config.get('enabled_tools', []),
            'add_to_agents': ["researcher"]
        }
        
        if protocol == 'stdio':
            config_data['command'] = "npx"
            config_data['args'] = server_config.get('args', [])
        else:
            # Build URL
            base_url = server_config.get('base_url', '')
            path = server_config.get('path', '')
            url = f"{base_url.rstrip('/')}{path}"
            
            # Add token for token auth
            auth_type = server_config.get('auth', '')
            token = server_config.get('token', '')
            
            if auth_type == 'token' and token:
                url = f"{url}?token={token}"
            
            config_data['url'] = url
            
            # Add headers for bearer auth
            if auth_type == 'bearer' and token:
                config_data['headers'] = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}"
                }
        
        servers[output_name] = config_data
    
    result = {"servers": servers}
    
    print("SUCCESS: Logic test completed")
    print("Result keys:", list(result.keys()))
    print("Server names:", list(result['servers'].keys()))
    
    for name, config in result['servers'].items():
        print(f"  {name}:")
        print(f"    transport: {config.get('transport')}")
        print(f"    url: {config.get('url', 'N/A')}")
        print(f"    enabled_tools: {len(config.get('enabled_tools', []))} tools")
        print(f"    headers: {'Yes' if config.get('headers') else 'No'}")
    
    # Verify expected results
    expected_servers = ['cloudbot', 'vm_coredump', 'antv']
    actual_servers = list(result['servers'].keys())
    
    print(f"\nExpected servers: {expected_servers}")
    print(f"Actual servers: {actual_servers}")
    print(f"Match: {set(expected_servers) == set(actual_servers)}")
    
    return set(expected_servers) == set(actual_servers)

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n✅ Test passed! The refactored logic works correctly.")
    else:
        print("\n❌ Test failed!")
        exit(1)
