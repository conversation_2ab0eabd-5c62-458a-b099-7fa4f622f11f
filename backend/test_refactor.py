#!/usr/bin/env python3
"""
测试重构后的常量系统
"""

def test_constants_import():
    """测试常量导入"""
    try:
        from common.constants import (
            APPLICATION_NAME,
            APP_HOME_DIR,
            WORKFLOW_MAX_STEPS,
            DEFAULT_MAX_STEP_NUM,
            AGENT_LLM_MAP,
            SELECTED_SEARCH_ENGINE,
            BUILT_IN_QUESTIONS,
            get_mcp_settings
        )
        print("✓ 所有常量导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_constants_values():
    """测试常量值"""
    try:
        from common.constants import (
            APPLICATION_NAME,
            WORKFLOW_MAX_STEPS,
            AGENT_LLM_MAP
        )
        
        print(f"应用名称: {APPLICATION_NAME}")
        print(f"最大步数: {WORKFLOW_MAX_STEPS}")
        print(f"Agent映射数量: {len(AGENT_LLM_MAP)}")
        
        # 验证类型
        assert isinstance(APPLICATION_NAME, str)
        assert isinstance(WORKFLOW_MAX_STEPS, int)
        assert isinstance(AGENT_LLM_MAP, dict)
        
        print("✓ 常量值验证成功")
        return True
    except Exception as e:
        print(f"✗ 常量值验证失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        from common.constants import (
            APP_HOME_DIR,
            DEFAULT_MAX_STEP_NUM,
            DEFAULT_AUTO_ACCEPTED_PLAN,
            headers,
            mcp_settings
        )
        
        print(f"旧常量 APP_HOME_DIR: {APP_HOME_DIR}")
        print(f"旧常量 DEFAULT_MAX_STEP_NUM: {DEFAULT_MAX_STEP_NUM}")
        print(f"旧常量 headers 类型: {type(headers)}")
        print(f"旧常量 mcp_settings 类型: {type(mcp_settings)}")
        
        print("✓ 向后兼容性验证成功")
        return True
    except Exception as e:
        print(f"✗ 向后兼容性验证失败: {e}")
        return False

def test_config_integration():
    """测试配置集成"""
    try:
        from config import (
            SELECTED_SEARCH_ENGINE,
            SearchEngine,
            BUILT_IN_QUESTIONS,
            AGENT_LLM_MAP
        )
        
        print(f"配置模块搜索引擎: {SELECTED_SEARCH_ENGINE}")
        print(f"配置模块问题数量: {len(BUILT_IN_QUESTIONS)}")
        print(f"配置模块Agent数量: {len(AGENT_LLM_MAP)}")
        
        print("✓ 配置集成验证成功")
        return True
    except Exception as e:
        print(f"✗ 配置集成验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试重构后的常量系统...")
    print("=" * 50)
    
    tests = [
        test_constants_import,
        test_constants_values,
        test_backward_compatibility,
        test_config_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n运行测试: {test.__name__}")
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    main()
