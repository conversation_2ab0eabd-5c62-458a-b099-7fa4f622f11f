
"""
Configuration Management System

简洁的配置管理系统，只暴露必要的对外接口：

核心接口：
- get_config() - 获取应用配置
- get_secret() - 获取加密密钥
- reload_config() - 重载配置

常用配置常量：
- 搜索引擎、问题列表、Agent映射等
"""

import os
from typing import Dict, Any, Optional

from .core.base_config import DotDict

# 全局配置缓存
_config_cache: Optional[Dict[str, Any]] = None


def get_config() -> DotDict:
    """获取应用配置"""
    global _config_cache
    if _config_cache is None:
        _config_cache = _load_config()
    return DotDict(_config_cache)


def get_secret(secret: str, pub_name: str = "ecs-deep-diagnose_aone_key") -> str:
    """获取解密后的密钥"""
    from .core.security_config import security_config
    return security_config.decrypt_secret(secret, pub_name)


def reload_config():
    """重载配置"""
    global _config_cache
    _config_cache = None


def _load_config() -> Dict[str, Any]:
    """内部函数：加载配置"""
    # 延迟导入避免循环依赖
    from dotenv import load_dotenv
    from .core.environment import get_environment, is_production_environment
    from .loaders.yaml_loader import YamlConfigLoader
    from .loaders.env_loader import EnvConfigLoader
    from common.constants.mcp import get_mcp_settings

    # 确保环境变量已加载
    load_dotenv()

    # 初始化加载器
    yaml_loader = YamlConfigLoader()
    env_loader = EnvConfigLoader()

    # 从YAML加载基础配置
    config = yaml_loader.load()

    # 环境变量覆盖
    env_config = env_loader.load()
    config.update(env_config)

    # 添加MCP设置
    config['mcp_settings'] = get_mcp_settings(DotDict(config))

    # 添加环境信息
    env = get_environment()
    config.update({
        'environment': env.value,
        'is_production': is_production_environment(env),
        'app_name': 'ecs-deep-diagnose',
        'app_env': env.value
    })

    return config


# ============================================================================
# 常用配置常量 - 延迟导入避免阻塞
# ============================================================================

def _get_selected_search_engine():
    from common.constants.tools import SELECTED_SEARCH_ENGINE
    return SELECTED_SEARCH_ENGINE

def _get_search_engine():
    from common.constants.tools import SearchEngine
    return SearchEngine

def _get_built_in_questions():
    from common.constants.questions import BUILT_IN_QUESTIONS
    return BUILT_IN_QUESTIONS

def _get_built_in_questions_zh_cn():
    from common.constants.questions import BUILT_IN_QUESTIONS_ZH_CN
    return BUILT_IN_QUESTIONS_ZH_CN

def _get_agent_llm_map():
    from common.constants.agents import AGENT_LLM_MAP
    return AGENT_LLM_MAP

def _get_llm_type():
    from common.constants.agents import LLMType
    return LLMType

def _get_load_yaml_config():
    from .loaders.yaml_loader import load_yaml_config
    return load_yaml_config

def _get_configuration():
    from .core.configuration import Configuration
    return Configuration

# 创建延迟加载的属性
SELECTED_SEARCH_ENGINE = _get_selected_search_engine()
SearchEngine = _get_search_engine()
BUILT_IN_QUESTIONS = _get_built_in_questions()
BUILT_IN_QUESTIONS_ZH_CN = _get_built_in_questions_zh_cn()
AGENT_LLM_MAP = _get_agent_llm_map()
LLMType = _get_llm_type()
load_yaml_config = _get_load_yaml_config()
Configuration = _get_configuration()


# ============================================================================
# 对外接口
# ============================================================================

__all__ = [
    # 核心接口
    "get_config",
    "get_secret",
    "reload_config",

    # 数据结构
    "DotDict",
    "Configuration",

    # 常用配置常量
    "SELECTED_SEARCH_ENGINE",
    "SearchEngine",
    "BUILT_IN_QUESTIONS",
    "BUILT_IN_QUESTIONS_ZH_CN",
    "AGENT_LLM_MAP",
    "LLMType",

    # 工具函数
    "load_yaml_config",
]
