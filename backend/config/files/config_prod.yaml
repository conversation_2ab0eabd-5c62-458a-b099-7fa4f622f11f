app:
  port: 8000
  secret: ecs_deep_diagnose_secret
  buc_host: https://login.alibaba-inc.com

url:
  ecs_deep_diagnose_endpoint: https://https://ecs-deep-diagnose.aliyun-inc.com/


keys:
  # secret config
  keycenter_pub_name: &keycenter_pub_name ecs-ai-middleoffice-key

# 密钥库：存储所有需要加密管理的密钥
security:
  secrets_vault:
    mcp_auth_token: &mcp_auth_token M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    langfuse_public_key: &langfuse_public_key [需要添加实际的加密密钥]
    langfuse_secret_key: &langfuse_secret_key [需要添加实际的加密密钥]
    langfuse_secret_key_public_evaluation: &langfuse_secret_key_public_evaluation [需要添加实际的加密密钥]
    langfuse_public_key_public_evaluation: &langfuse_public_key_public_evaluation [需要添加实际的加密密钥]
    langfuse_admin_password: &langfuse_admin_password [需要添加实际的加密密钥]

# MCP服务器配置
services:
  mcp_servers:
    cloudbot:
      base_url: http://pre-xmca-cloudbot.aliyun-inc.com
      sources:
        diagnose:
          path: /mcp/mcp/
          token: ""

    ecs_mcp:
      base_url: https://ecs-mcp.alibaba-inc.com
      sources:
        vm_coredump:
          path: /vm_coredump/mcp/
          token: ************************************************

langfuse:
  public_key:  !decrypt [ *langfuse_public_key, *keycenter_pub_name ]
  secret_key:  !decrypt [ *langfuse_secret_key, *keycenter_pub_name ]
  secret_key_public_evaluation: !decrypt [ *langfuse_secret_key_public_evaluation, *keycenter_pub_name ]
  public_key_public_evaluation: !decrypt [ *langfuse_public_key_public_evaluation, *keycenter_pub_name ]

  user_name: <EMAIL>
  password:   !decrypt [ *langfuse_admin_password, *keycenter_pub_name ]

llm:
  tongyi:
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    secret: !decrypt [ *tongyi_sk, *keycenter_pub_name ]
