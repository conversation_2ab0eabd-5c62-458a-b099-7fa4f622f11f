app:
  port: 8000
  secret: ecs_deep_diagnose

auth:
  buc_sso:
    host: https://login.alibaba-inc.com
    app_code: 40cbff03-d950-4b8e-8f38-e7b14f4c631d
  jwt_users:
    - user_name: admin
      password: admin
    - user_name: viewer
      password: viewer



mcp_servers:
    vm_coredump:
      protocol: streamable_http
      base_url: https://ecs-mcp.alibaba-inc.com
      path: /vm_coredump/mcp/
      token: ************************************************
      auth: token
      enabled_tools:
        - getVmBasicInfo
        - getNcBasicInfo
        - runDiagnose
        - listReportedOperationalEvents
        - listMonitorExceptions
        - listActionTrail
        - ScreenShotDiagnose
        - listOperationRecords
        - listChangeRecords
        - SubmitOps

    diagnose:
      protocol: streamable_http
      base_url: http://pre-xmca-cloudbot.aliyun-inc.com

      path: /mcp/mcp/
      token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
      auth: bearer
      enabled_tools:
        - get_vm_coredump

    antv:
      protocol: stdio
      args:
        - -y
        - "@antv/mcp-server-chart"
      enabled_tools:
        - generate_pie_chart



security:
  # 定义用于解密的公钥名锚点(&keycenter_pub_name)，供全局引用
  key_center_public_name: &keycenter_pub_name ecs-deep-diagnose_aone_key

  # 密钥库：存储所有需要加密管理的密钥
  secrets_vault:
    qwen_api_key: &qwen_ak vZnIEeRgVzAzDkDlULclRt6TsbvuG/s4lHnf9PYHb73bo5E2hwCiozUgFBDdecVJ
    langfuse_secret_key: &langfuse_secret_key Jp9iO1h92IOBdD++hf0baAG0KuUGVJDYbKDfTXAAopKTrMlfX2pnqmUwLJSGcZNv
    langfuse_public_key: &langfuse_public_key blWQfahUx4KUxcLhdBprNYLUI7rsSEwe8PBid0uf4dXegx+KNErFY5ctczMunh/j

llm:
  tongyi_provider:
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]

  profiles:
    reasoning:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen3-235b-a22b"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
    basic:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen-max-latest"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
    vision:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen-max-latest"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]

observability:
  langfuse:
    public_key: !decrypt [ *langfuse_public_key, *keycenter_pub_name ]
    secret_key: !decrypt [ *langfuse_secret_key, *keycenter_pub_name ]
