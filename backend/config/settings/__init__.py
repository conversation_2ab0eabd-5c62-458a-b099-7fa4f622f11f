"""
Configuration settings modules.

This module provides backward compatibility by re-exporting constants
from the original settings files. The constants have been moved to
common.constants but we maintain the original imports here.
"""

# Import from original files to maintain backward compatibility
from .mcp_servers import get_mcp_settings, MCPServerConfig
from .agents import AGENT_LLM_MAP, LLMType
from .tools import SELECTED_SEARCH_ENGINE, SearchEngine
from .questions import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN

__all__ = [
    'get_mcp_settings',
    'MCPServerConfig',
    'AGENT_LLM_MAP',
    'LLMType',
    'SELECTED_SEARCH_ENGINE',
    'SearchEngine',
    'BUILT_IN_QUESTIONS',
    'BUILT_IN_QUESTIONS_ZH_CN'
]
