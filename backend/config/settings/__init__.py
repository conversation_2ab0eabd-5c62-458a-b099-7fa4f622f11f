"""
Configuration settings modules.

This module provides backward compatibility by re-exporting constants
from the new common.constants module structure.
"""

# Import from new constants structure
from common.constants.mcp import get_mcp_settings, MCPServerConfig
from common.constants.agents import AGENT_LLM_MAP, LLMType
from common.constants.tools import SELECTED_SEARCH_ENGINE, SearchEngine
from common.constants.questions import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN

__all__ = [
    'get_mcp_settings',
    'MCPServerConfig',
    'AGENT_LLM_MAP',
    'LLMType',
    'SELECTED_SEARCH_ENGINE',
    'SearchEngine',
    'BUILT_IN_QUESTIONS',
    'BUILT_IN_QUESTIONS_ZH_CN'
]
