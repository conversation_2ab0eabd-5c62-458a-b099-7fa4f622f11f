"""
Common constants module.

This module provides centralized access to all application constants,
organized by functional areas for better maintainability and clarity.
"""

# Import from organized constant modules
from .app import *
from .workflow import *
from .mcp import *
from .agents import *
from .tools import *
from .questions import *

__all__ = [
    # Application constants
    'APPLICATION_NAME',
    'APPLICATION_HOME_DIRECTORY',
    'APP_HOME_DIR',  # Legacy name

    # Workflow constants
    'WORKFLOW_MAX_STEPS',
    'WORKFLOW_AUTO_ACCEPT_PLAN',
    'WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION',
    'DEFAULT_MAX_STEP_NUM',  # Legacy name
    'DEFAULT_AUTO_ACCEPTED_PLAN',  # Legacy name
    'DEFAULT_ENABLE_BACKGROUND_INVESTIGATION',  # Legacy name

    # MCP constants
    'MCPServerConfig',
    'get_mcp_settings',
    'get_default_headers',
    'get_legacy_mcp_settings',
    'get_legacy_headers',
    'headers',  # Legacy name
    'mcp_settings',  # Legacy name

    # Agent constants
    'LLMType',
    'AGENT_LLM_MAP',

    # Tool constants
    'SearchEngine',
    'SELECTED_SEARCH_ENGINE',

    # Question constants
    'BUILT_IN_QUESTIONS',
    'BUILT_IN_QUESTIONS_ZH_CN'
]