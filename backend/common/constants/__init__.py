"""
Common constants module.

This module provides centralized access to all application constants,
organized by functional areas for better maintainability and clarity.
"""

# Import from deprecated constants for backward compatibility
from ..constants_deprecated import (
    APP_HOME_DIR,
    DEFAULT_MAX_STEP_NUM,
    DEFAULT_AUTO_ACCEPTED_PLAN,
    DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
    headers,
    mcp_settings
)

# Import from new organized constant modules
from .app import *
from .workflow import *
from .mcp import *
from .agents import *
from .tools import *
from .questions import *

__all__ = [
    # Legacy constants (for backward compatibility)
    'APP_HOME_DIR',
    'DEFAULT_MAX_STEP_NUM',
    'DEFAULT_AUTO_ACCEPTED_PLAN',
    'DEFAULT_ENABLE_BACKGROUND_INVESTIGATION',
    'headers',
    'mcp_settings',

    # New organized constants
    'APPLICATION_NAME',
    'APPLICATION_HOME_DIRECTORY',
    'WORKFLOW_MAX_STEPS',
    'WORKFLOW_AUTO_ACCEPT_PLAN',
    'WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION',
    'MCP_SERVER_CONFIG',
    'MCP_DEFAULT_HEADERS',
    'get_mcp_settings',
    'MCPServerConfig',
    'AGENT_LLM_MAPPING',
    'LLMType',
    'SEARCH_ENGINE_SELECTION',
    'SearchEngine',
    'BUILTIN_QUESTIONS',
    'BUILTIN_QUESTIONS_ZH_CN'
]