"""
Workflow-related constants.

This module contains constants that control workflow behavior,
such as step limits, auto-acceptance settings, and investigation features.
"""

def _get_workflow_config():
    """Get workflow configuration from config system."""
    try:
        from config import get_config
        config = get_config()
        return config.workflow
    except (ImportError, AttributeError):
        # Fallback to default values if config is not available
        return None

def _get_workflow_max_steps():
    """Get workflow max steps from config or default."""
    workflow_config = _get_workflow_config()
    if workflow_config and hasattr(workflow_config, 'max_steps'):
        return workflow_config.max_steps
    return 20  # Default fallback

def _get_workflow_auto_accept_plan():
    """Get workflow auto accept plan setting from config or default."""
    workflow_config = _get_workflow_config()
    if workflow_config and hasattr(workflow_config, 'auto_accept_plan'):
        return workflow_config.auto_accept_plan
    return True  # Default fallback

def _get_workflow_enable_background_investigation():
    """Get workflow background investigation setting from config or default."""
    workflow_config = _get_workflow_config()
    if workflow_config and hasattr(workflow_config, 'enable_background_investigation'):
        return workflow_config.enable_background_investigation
    return False  # Default fallback

# Workflow execution limits - use defaults to avoid import issues during module loading
WORKFLOW_MAX_STEPS = 20

# Workflow behavior settings
WORKFLOW_AUTO_ACCEPT_PLAN = True
WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION = False

# For backward compatibility
DEFAULT_MAX_STEP_NUM = WORKFLOW_MAX_STEPS
DEFAULT_AUTO_ACCEPTED_PLAN = WORKFLOW_AUTO_ACCEPT_PLAN
DEFAULT_ENABLE_BACKGROUND_INVESTIGATION = WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION

__all__ = [
    'WORKFLOW_MAX_STEPS',
    'WORKFLOW_AUTO_ACCEPT_PLAN',
    'WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION',
    # Legacy names
    'DEFAULT_MAX_STEP_NUM',
    'DEFAULT_AUTO_ACCEPTED_PLAN',
    'DEFAULT_ENABLE_BACKGROUND_INVESTIGATION'
]
