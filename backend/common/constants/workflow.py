"""
Workflow-related constants.

This module contains constants that control workflow behavior,
such as step limits, auto-acceptance settings, and investigation features.
"""

# Workflow execution limits
WORKFLOW_MAX_STEPS = 20  # Maximum number of steps in a workflow

# Workflow behavior settings
WORKFLOW_AUTO_ACCEPT_PLAN = True  # Whether to automatically accept execution plans
WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION = False  # Whether to enable background investigation

# For backward compatibility
DEFAULT_MAX_STEP_NUM = WORKFLOW_MAX_STEPS
DEFAULT_AUTO_ACCEPTED_PLAN = WORKFLOW_AUTO_ACCEPT_PLAN
DEFAULT_ENABLE_BACKGROUND_INVESTIGATION = WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION

__all__ = [
    'WORKFLOW_MAX_STEPS',
    'WORKFLOW_AUTO_ACCEPT_PLAN', 
    'WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION',
    # Legacy names
    'DEFAULT_MAX_STEP_NUM',
    'DEFAULT_AUTO_ACCEPTED_PLAN',
    'DEFAULT_ENABLE_BACKGROUND_INVESTIGATION'
]
