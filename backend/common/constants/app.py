"""
Application-level constants.

This module contains constants related to the application itself,
such as application name, directories, and basic configuration.
"""

def _get_app_config():
    """Get application configuration from config system."""
    try:
        from config import get_config
        config = get_config()
        return config.app
    except (ImportError, AttributeError):
        # Fallback to default values if config is not available
        return None

def _get_app_name():
    """Get application name from config or default."""
    app_config = _get_app_config()
    if app_config and hasattr(app_config, 'name'):
        return app_config.name
    return 'ecs-deep-diagnose'  # Default fallback

def _get_app_home_directory():
    """Get application home directory from config or default."""
    app_config = _get_app_config()
    if app_config and hasattr(app_config, 'home_directory'):
        return app_config.home_directory
    return '/home/<USER>/ecs-deep-diagnose'  # Default fallback

# Application identity
APPLICATION_NAME = _get_app_name()
APPLICATION_HOME_DIRECTORY = _get_app_home_directory()

# For backward compatibility
APP_HOME_DIR = APPLICATION_HOME_DIRECTORY

__all__ = [
    'APPLICATION_NAME',
    'APPLICATION_HOME_DIRECTORY',
    'APP_HOME_DIR'  # Legacy name
]
