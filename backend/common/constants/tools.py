"""
Tool configuration constants.

This module contains tool-related configurations, including
search engine settings and tool selection preferences.
"""

import os
import enum
from dotenv import load_dotenv

# Ensure environment variables are loaded
load_dotenv()

class SearchEngine(enum.Enum):
    """Available search engines."""
    TAVILY = "tavily"
    DUCKDUCKGO = "duckduckgo"
    BRAVE_SEARCH = "brave_search"
    ARXIV = "arxiv"

# Search engine selection configuration
SEARCH_ENGINE_SELECTION = os.getenv("SEARCH_API", SearchEngine.TAVILY.value)

# For backward compatibility
SELECTED_SEARCH_ENGINE = SEARCH_ENGINE_SELECTION

__all__ = [
    'SearchEngine',
    'SEARCH_ENGINE_SELECTION',
    # Legacy name
    'SELECTED_SEARCH_ENGINE'
]
