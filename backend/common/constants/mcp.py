"""
MCP (Model Context Protocol) server configuration constants.

This module contains MCP server configurations, including server definitions,
authentication headers, and utility functions for MCP server management.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class MCPServerConfig:
    """Configuration for a single MCP server."""
    name: str
    transport: str
    enabled_tools: List[str]
    add_to_agents: List[str]
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    command: Optional[str] = None
    args: Optional[List[str]] = None
    headers: Optional[Dict[str, str]] = None

def get_mcp_settings(config: Optional[Any] = None) -> Dict[str, Any]:
    """
    Get MCP server settings configuration.

    This is a simplified version that returns the static configuration.
    For dynamic configuration, use the original function from config.settings.mcp_servers.
    """
    return MCP_SERVER_CONFIG

# Default MCP headers configuration
MCP_DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="
}

# Legacy MCP server configuration (from constants_deprecated.py)
MCP_SERVER_CONFIG = {
    "servers": {
        "cloudbot": {
            "name": "cloudbot",
            "transport": "streamable_http",
            "env": None,
            "url": "http://pre-xmca-cloudbot.aliyun-inc.com/mcp/mcp/",
            "enabled_tools": [
                "getVmBasicInfo",
                "getNcBasicInfo", 
                "runDiagnose",
                "listReportedOperationalEvents",
                "listMonitorExceptions",
                "listActionTrail",
                "ScreenShotDiagnose",
                "listOperationRecords",
                "listChangeRecords",
                "SubmitOps",
            ],
            "add_to_agents": ["researcher"],
            "headers": MCP_DEFAULT_HEADERS
        },
        "vm_coredump": {
            "name": "vm_coredump",
            "transport": "streamable_http",
            "env": None,
            "url": "http://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/?token=************************************************",
            "enabled_tools": [
                "get_vm_coredump"
            ],
            "add_to_agents": ["researcher"]
        },
        "antv": {
            "name": "antv",
            "transport": "stdio",
            "env": None,
            "command": "npx",
            "args": [
                "-y",
                "@antv/mcp-server-chart"
            ],
            "enabled_tools": [
                "generate_pie_chart"
            ],
            "add_to_agents": ["researcher"]
        }
    }
}

# For backward compatibility
headers = MCP_DEFAULT_HEADERS
mcp_settings = MCP_SERVER_CONFIG

def get_default_headers(config: Optional[Any] = None) -> Dict[str, str]:
    """
    Get default headers for MCP requests.

    Returns the default MCP headers.
    """
    return MCP_DEFAULT_HEADERS

__all__ = [
    'MCPServerConfig',
    'get_mcp_settings',
    'MCP_DEFAULT_HEADERS',
    'MCP_SERVER_CONFIG',
    'get_default_headers',
    # Legacy names
    'headers',
    'mcp_settings'
]
