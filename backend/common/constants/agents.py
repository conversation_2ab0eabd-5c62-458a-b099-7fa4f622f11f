"""
Agent configuration constants.

This module contains agent-related configurations, including
LLM type definitions and agent-to-LLM mappings.
"""

from typing import Literal

# Define available LLM types
LLMType = Literal["basic", "reasoning", "vision"]

# Agent to LLM mapping configuration
AGENT_LLM_MAPPING: dict[str, LLMType] = {
    "coordinator": "reasoning",
    "planner": "reasoning",
    "researcher": "reasoning",
    "coder": "basic",
    "reporter": "reasoning",
    "podcast_script_writer": "basic",
    "ppt_composer": "basic",
    "prose_writer": "basic",
}

# For backward compatibility
AGENT_LLM_MAP = AGENT_LLM_MAPPING

__all__ = [
    'LLMType',
    'AGENT_LLM_MAPPING',
    # Legacy name
    'AGENT_LLM_MAP'
]
