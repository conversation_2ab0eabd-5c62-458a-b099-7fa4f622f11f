# 配置系统文档

本目录包含 ECS Deep Diagnose 项目的配置系统相关文档。

## 文档列表

- [配置系统概述](./overview.md) - 配置系统的整体架构和设计理念
- [常量配置系统](./constants.md) - 常量定义和使用方法
- [YAML配置文件](./yaml-config.md) - YAML配置文件的结构和使用方法
- [环境变量配置](./env-config.md) - 环境变量的配置和管理
- [安全配置](./security-config.md) - 密钥管理和安全配置
- [配置加载器](./loaders.md) - 配置加载器的实现和扩展

## 快速开始

1. 查看 [配置系统概述](./overview.md) 了解整体架构
2. 阅读 [常量配置系统](./constants.md) 了解如何使用常量
3. 参考 [YAML配置文件](./yaml-config.md) 配置你的环境
4. 使用 [环境变量配置](./env-config.md) 进行环境特定的配置
5. 阅读 [安全配置](./security-config.md) 了解密钥管理

## 配置文件位置

- 开发环境: `backend/config/files/config_daily.yaml`
- 生产环境: `backend/config/files/config_prod.yaml`

## 使用示例

### 使用配置系统

```python
from config import get_config

# 获取配置
config = get_config()

# 访问配置项
app_name = config.app.name
port = config.app.port
```

### 使用常量系统

```python
from common.constants import (
    APPLICATION_NAME,
    WORKFLOW_MAX_STEPS,
    AGENT_LLM_MAP,
    get_mcp_settings
)

# 使用应用常量
print(f"应用名称: {APPLICATION_NAME}")

# 使用工作流常量
max_steps = WORKFLOW_MAX_STEPS

# 使用MCP配置
mcp_config = get_mcp_settings()
```

## 重构说明

项目已完成配置系统重构：

1. **常量集中管理** - 所有常量现在统一在 `backend/common/constants/` 目录下管理
2. **配置文件驱动** - 常量值从YAML配置文件中读取，支持环境特定配置
3. **向后兼容** - 保持了旧的导入路径和常量名称的兼容性
4. **清晰的层次结构** - 按功能领域组织常量模块

详细信息请参考 [常量配置系统](./constants.md) 文档。