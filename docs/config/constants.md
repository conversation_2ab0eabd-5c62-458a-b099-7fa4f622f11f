# 常量配置系统文档

## 概述

本文档描述了 ECS Deep Diagnose 项目的常量配置系统，包括如何定义配置、如何使用常量，以及配置文件的结构。

## 目录结构

```
backend/
├── common/
│   └── constants/           # 常量定义目录
│       ├── __init__.py     # 统一导出接口
│       ├── app.py          # 应用级常量
│       ├── workflow.py     # 工作流常量
│       ├── mcp.py          # MCP服务器配置
│       ├── agents.py       # Agent配置
│       ├── tools.py        # 工具配置
│       └── questions.py    # 内置问题配置
└── config/
    └── files/
        ├── config_daily.yaml    # 日常环境配置
        └── config_prod.yaml     # 生产环境配置
```

## 配置文件结构

### config_daily.yaml / config_prod.yaml

```yaml
# 应用配置
app:
  name: ecs-deep-diagnose                    # 应用名称
  home_directory: /home/<USER>/ecs-deep-diagnose  # 应用主目录
  port: 8000                                 # 服务端口
  secret: ecs_deep_diagnose                  # 应用密钥

# 工作流配置
workflow:
  max_steps: 20                              # 最大执行步数
  auto_accept_plan: true                     # 是否自动接受执行计划
  enable_background_investigation: false     # 是否启用后台调查

# MCP服务器配置
mcp_servers:
  vm_coredump:                              # 服务器名称
    protocol: streamable_http               # 协议类型
    base_url: https://ecs-mcp.alibaba-inc.com  # 基础URL
    path: /vm_coredump/mcp/                 # 路径
    token: your_token_here                  # 认证令牌
    auth: token                             # 认证方式
    enabled_tools:                          # 启用的工具列表
      - getVmBasicInfo
      - getNcBasicInfo
      # ... 更多工具

  diagnose:                                 # 诊断服务器
    protocol: streamable_http
    base_url: http://pre-xmca-cloudbot.aliyun-inc.com
    path: /mcp/mcp/
    token: your_bearer_token_here
    auth: bearer
    enabled_tools:
      - get_vm_coredump

  antv:                                     # AntV图表服务器
    protocol: stdio                         # 标准输入输出协议
    args:                                   # 命令行参数
      - -y
      - "@antv/mcp-server-chart"
    enabled_tools:
      - generate_pie_chart
```

## 常量使用方法

### 1. 导入常量

```python
# 从统一接口导入
from common.constants import (
    APPLICATION_NAME,
    WORKFLOW_MAX_STEPS,
    AGENT_LLM_MAP,
    SELECTED_SEARCH_ENGINE,
    BUILT_IN_QUESTIONS
)

# 或者从具体模块导入
from common.constants.app import APPLICATION_NAME
from common.constants.workflow import WORKFLOW_MAX_STEPS
from common.constants.mcp import get_mcp_settings
```

### 2. 使用应用常量

```python
from common.constants import APPLICATION_NAME, APP_HOME_DIR

print(f"应用名称: {APPLICATION_NAME}")
print(f"应用目录: {APP_HOME_DIR}")
```

### 3. 使用工作流常量

```python
from common.constants import (
    WORKFLOW_MAX_STEPS,
    WORKFLOW_AUTO_ACCEPT_PLAN,
    WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION
)

# 在工作流中使用
def create_workflow():
    return Workflow(
        max_steps=WORKFLOW_MAX_STEPS,
        auto_accept=WORKFLOW_AUTO_ACCEPT_PLAN,
        background_investigation=WORKFLOW_ENABLE_BACKGROUND_INVESTIGATION
    )
```

### 4. 使用MCP配置

```python
from common.constants import get_mcp_settings, MCPServerConfig

# 获取MCP服务器配置
mcp_config = get_mcp_settings()
servers = mcp_config["servers"]

# 遍历服务器配置
for server_name, server_config in servers.items():
    print(f"服务器: {server_name}")
    print(f"传输协议: {server_config['transport']}")
    print(f"启用工具: {server_config['enabled_tools']}")
```

## 向后兼容性

为了保持向后兼容性，系统提供了旧的常量名称：

```python
# 新的推荐用法
from common.constants import APPLICATION_NAME, WORKFLOW_MAX_STEPS

# 旧的兼容用法（仍然可用）
from common.constants import APP_HOME_DIR, DEFAULT_MAX_STEP_NUM
```

## 配置优先级

1. **YAML配置文件** - 最高优先级，从 config_daily.yaml 或 config_prod.yaml 读取
2. **环境变量** - 中等优先级，可以覆盖YAML配置
3. **默认值** - 最低优先级，当配置不可用时使用

## 添加新配置

### 1. 在YAML文件中添加配置

```yaml
# 在 config_daily.yaml 中添加新的配置节
new_feature:
  enabled: true
  timeout: 30
  retry_count: 3
```

### 2. 创建对应的常量模块

```python
# backend/common/constants/new_feature.py
"""
新功能配置常量
"""

def _get_new_feature_config():
    """从配置系统获取新功能配置"""
    try:
        from config import get_config
        config = get_config()
        return config.new_feature
    except (ImportError, AttributeError):
        return None

def _get_feature_enabled():
    """获取功能启用状态"""
    feature_config = _get_new_feature_config()
    if feature_config and hasattr(feature_config, 'enabled'):
        return feature_config.enabled
    return False  # 默认值

# 导出常量
NEW_FEATURE_ENABLED = _get_feature_enabled()
NEW_FEATURE_TIMEOUT = _get_feature_timeout()
NEW_FEATURE_RETRY_COUNT = _get_feature_retry_count()

__all__ = [
    'NEW_FEATURE_ENABLED',
    'NEW_FEATURE_TIMEOUT', 
    'NEW_FEATURE_RETRY_COUNT'
]
```

### 3. 在 __init__.py 中导出

```python
# backend/common/constants/__init__.py
from .new_feature import *

__all__ = [
    # ... 现有导出
    'NEW_FEATURE_ENABLED',
    'NEW_FEATURE_TIMEOUT',
    'NEW_FEATURE_RETRY_COUNT'
]
```

## 最佳实践

1. **配置分组** - 将相关配置放在同一个YAML节点下
2. **命名规范** - 使用清晰、描述性的名称
3. **默认值** - 总是提供合理的默认值
4. **文档** - 为每个配置项添加注释说明
5. **类型安全** - 在Python代码中使用类型提示
6. **向后兼容** - 保持旧的常量名称可用

## 故障排除

### 常见问题

1. **导入错误** - 确保模块路径正确
2. **配置不生效** - 检查YAML文件语法和缩进
3. **默认值问题** - 确保提供了合理的默认值
4. **循环导入** - 避免在配置模块中导入业务逻辑模块

### 调试方法

```python
# 检查配置是否正确加载
from config import get_config
config = get_config()
print(config.__dict__)

# 检查常量值
from common.constants import APPLICATION_NAME
print(f"应用名称: {APPLICATION_NAME}")
```
